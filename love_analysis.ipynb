import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os

font_path = "NanumGothicCoding-Regular.ttf"
url = "https://github.com/emersion/nanum-gothic-coding/raw/master/fonts/NanumGothicCoding-Regular.ttf"
if not os.path.exists(font_path):
    import urllib.request
    urllib.request.urlretrieve(url, font_path)

fm.fontManager.addfont(font_path)
plt.rc('font', family='NanumGothicCoding')
plt.rcParams['axes.unicode_minus'] = False


# === Initial setup (uncomment if you do not have the libraries) ===
# !pip install spacy nltk konlpy matplotlib
# !python -m spacy download en_core_web_sm
# !python -m spacy download fr_core_news_sm

import re, math, collections, itertools
import matplotlib.pyplot as plt
import nltk, spacy

# SpaCy language objects (download first if missing)
try:
    nlp_en = spacy.load('en_core_web_sm')
    nlp_fr = spacy.load('fr_core_news_sm')
except OSError:
    print('⚠️  SpaCy models not found. Run the pip & download commands above.')

# Korean tokenizer
from konlpy.tag import Okt
okt = Okt()

nltk.download('punkt', quiet=True)


# === Load the three input files ===
with open('korean.txt', 'r', encoding='utf-8') as f:
    text_ko = f.read()
with open('english.txt', 'r', encoding='utf-8') as f:
    text_en = f.read()
with open('french.txt', 'r', encoding='utf-8') as f:
    text_fr = f.read()

print('Files loaded.')

# === Define love keywords (lower‑case, no accents needed here) ===
LOVE_KEYWORDS = {
    'korean': ['사랑', '사랑하다', '좋아', '좋아하다'],
    'english': ['love', 'loves', 'loving', 'loved', 'adore', 'adores', 'adoring', 'adored'],
    'french' : ['amour', 'amours', 'aimer', 'aime', 'aimes', 'aiment', 'aimé', 'aimée', 'adorer', 'adore', 'adores']
}

def split_sentences(text, lang):
    """Return list of sentences using SpaCy for EN/FR, simple regex otherwise."""
    if lang == 'english':
        return [sent.text.strip() for sent in nlp_en(text).sents]
    if lang == 'french':
        return [sent.text.strip() for sent in nlp_fr(text).sents]
    # Korean fallback: split by punctuation (., !, ?)
    return re.split(r'[.!?]\s+', text)


def extract_tokens(sentence, lang):
    """Return list of (lemma, pos) tuples for nouns & verbs only."""
    tokens = []
    if lang == 'english':
        doc = nlp_en(sentence)
        tokens = [(tok.lemma_.lower(), tok.pos_) for tok in doc if tok.pos_ in {'NOUN', 'VERB'}]
    elif lang == 'french':
        doc = nlp_fr(sentence)
        tokens = [(tok.lemma_.lower(), tok.pos_) for tok in doc if tok.pos_ in {'NOUN', 'VERB'}]
    else:  # Korean
        for word, pos in okt.pos(sentence, stem=True):
            if pos.startswith('N') or pos == 'Verb':
                tokens.append((word, 'NOUN' if pos.startswith('N') else 'VERB'))
    return [tok for tok, p in tokens]


def prepare_language(text, lang):
    sentences = split_sentences(text, lang)
    love_set = set(LOVE_KEYWORDS[lang])
    love_sentences = [s for s in sentences if any(k in s.lower() for k in love_set)]

    # Token lists
    all_tokens = list(itertools.chain.from_iterable(extract_tokens(s, lang) for s in sentences))
    love_tokens = list(itertools.chain.from_iterable(extract_tokens(s, lang) for s in love_sentences))

    # Frequency counts
    freq_all = collections.Counter(all_tokens)
    freq_love = collections.Counter(love_tokens)

    return {
        'sentences': sentences,
        'love_sentences': love_sentences,
        'freq_all': freq_all,
        'freq_love': freq_love,
        'total_sents': len(sentences),
        'total_sents_love': len(love_sentences)
    }

corpora = {
    'korean': prepare_language(text_ko, 'korean'),
    'english': prepare_language(text_en, 'english'),
    'french' : prepare_language(text_fr, 'french')
}
print('Corpora prepared.')

def calc_pmi(lang_data, word, smoothing=1):
    # Sentence‑level PMI between word and love‑keyword set
    total = lang_data['total_sents'] + smoothing
    total_love = lang_data['total_sents_love'] + smoothing

    # Sentences containing the word
    sents_with_word = sum(1 for s in lang_data['sentences'] if word in s.lower()) + smoothing

    # Sentences containing both love and word
    sents_both = sum(1 for s in lang_data['love_sentences'] if word in s.lower()) + smoothing

    p_love = total_love / total
    p_word = sents_with_word / total
    p_both = sents_both / total
    return math.log2(p_both / (p_love * p_word))

# Compute PMI for each language for top words in love sentences
top_n = 30  # adjust if desired
results = {}
for lang, data in corpora.items():
    common = [w for w, c in data['freq_love'].most_common(top_n)]
    pmi_scores = {w: calc_pmi(data, w) for w in common}
    results[lang] = pmi_scores

print('PMI calculated.')

import matplotlib.pyplot as plt

def plot_top(lang, metric='freq', top_k=15):
    data = corpora[lang]
    if metric == 'freq':
        scores = data['freq_love']
    else:
        scores = results[lang]
    items = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:top_k]
    words, vals = zip(*items)
    plt.figure(figsize=(8,4))
    plt.bar(range(len(words)), vals)
    plt.xticks(range(len(words)), words, rotation=45, ha='right')
    plt.xlabel('Word')
    plt.ylabel('Count' if metric=='freq' else 'PMI')
    plt.title(f"{lang.capitalize()} top {metric.upper()} words in love sentences")
    plt.tight_layout()
    plt.show()


def show_top_words_text(lang, metric='freq', top_k=15):
    data = corpora[lang]
    if metric == 'freq':
        scores = data['freq_love']
    else:
        scores = results[lang]
    items = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:top_k]
    print(f"\nTop {top_k} {metric.upper()} words in {lang.capitalize()} love sentences:")
    print(f"{'Rank':<4} {'Word':<20} {'Count' if metric=='freq' else 'PMI':>10}")
    print('-' * 38)
    for i, (word, val) in enumerate(items, 1):
        print(f"{i:<4} {word:<20} {val:>10.3f}" if metric != 'freq' else f"{i:<4} {word:<20} {val:>10}")
    print()

# Usage together with plot
for lang in ['korean', 'english', 'french']:
    show_top_words_text(lang, 'freq')
    plot_top(lang, 'freq')
    show_top_words_text(lang, 'pmi')
    plot_top(lang, 'pmi')
