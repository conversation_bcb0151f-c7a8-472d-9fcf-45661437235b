# --- 1. Setup: Install and set NanumGothicCoding for Hangul support in matplotlib ---
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os

font_path = "NanumGothicCoding-Regular.ttf"
font_url = "https://github.com/emersion/nanum-gothic-coding/raw/master/fonts/NanumGothicCoding-Regular.ttf"
if not os.path.exists(font_path):
    import urllib.request
    urllib.request.urlretrieve(font_url, font_path)
fm.fontManager.addfont(font_path)
plt.rc('font', family='NanumGothicCoding')
plt.rcParams['axes.unicode_minus'] = False

# --- 2. Import dependencies ---
import re, math, collections, itertools
import pandas as pd
import nltk, spacy
from konlpy.tag import Okt

nltk.download('punkt', quiet=True)
nlp_en = spacy.load('en_core_web_sm')
nlp_fr = spacy.load('fr_core_news_sm')
okt = Okt()

# --- 3. Load texts ---
with open('love data text.txt', 'r', encoding='utf-8') as f:
    text_ko = f.read()
with open('love data text.txt', 'r', encoding='utf-8') as f:
    text_en = f.read()
with open('love data text.txt', 'r', encoding='utf-8') as f:
    text_fr = f.read()
print('Files loaded.')

# --- 4. Define love keywords ---
LOVE_KEYWORDS = {
    'korean': ['사랑', '사랑하다', '좋아', '좋아하다'],
    'english': ['love', 'loves', 'loving', 'loved', 'adore', 'adores', 'adoring', 'adored'],
    'french' : ['amour', 'amours', 'aimer', 'aime', 'aimes', 'aiment', 'aimé', 'aimée', 'adorer', 'adore', 'adores']
}

# --- 5. Helper functions ---
def split_sentences(text, lang):
    if lang == 'english':
        return [sent.text.strip() for sent in nlp_en(text).sents]
    if lang == 'french':
        return [sent.text.strip() for sent in nlp_fr(text).sents]
    return re.split(r'[.!?]\s+', text)

def extract_tokens(sentence, lang):
    tokens = []
    if lang == 'english':
        doc = nlp_en(sentence)
        tokens = [(tok.lemma_.lower(), tok.pos_) for tok in doc if tok.pos_ in {'NOUN', 'VERB'}]
    elif lang == 'french':
        doc = nlp_fr(sentence)
        tokens = [(tok.lemma_.lower(), tok.pos_) for tok in doc if tok.pos_ in {'NOUN', 'VERB'}]
    else:
        for word, pos in okt.pos(sentence, stem=True):
            if pos.startswith('N') or pos == 'Verb':
                tokens.append((word, 'NOUN' if pos.startswith('N') else 'VERB'))
    return [tok for tok, p in tokens]

def prepare_language(text, lang):
    sentences = split_sentences(text, lang)
    love_set = set(LOVE_KEYWORDS[lang])
    love_sentences = [s for s in sentences if any(k in s.lower() for k in love_set)]
    all_tokens = list(itertools.chain.from_iterable(extract_tokens(s, lang) for s in sentences))
    love_tokens = list(itertools.chain.from_iterable(extract_tokens(s, lang) for s in love_sentences))
    freq_all = collections.Counter(all_tokens)
    freq_love = collections.Counter(love_tokens)
    return {
        'sentences': sentences,
        'love_sentences': love_sentences,
        'freq_all': freq_all,
        'freq_love': freq_love,
        'total_sents': len(sentences),
        'total_sents_love': len(love_sentences)
    }

corpora = {
    'korean': prepare_language(text_ko, 'korean'),
    'english': prepare_language(text_en, 'english'),
    'french' : prepare_language(text_fr, 'french')
}

# --- 6. Frequency table function (exclude love keywords) ---
def show_top_words_text(lang, top_k=15):
    freq_love = corpora[lang]['freq_love']
    exclude = set(LOVE_KEYWORDS[lang])
    items = [(w, c) for w, c in freq_love.most_common() if w not in exclude][:top_k]
    print(f"\nTop {top_k} FREQ words in {lang.capitalize()} love sentences:")
    print(f"{'Rank':<4} {'Word':<24} {'Count':>8}")
    print('-' * 38)
    for i, (word, val) in enumerate(items, 1):
        print(f"{i:<4} {word:<24} {val:>8}")
    return [w for w, _ in items]

# --- 7. For each language, get top 15 words in love sentences (excluding keywords) ---
top_words = {}
for lang in ['korean', 'english', 'french']:
    top_words[lang] = show_top_words_text(lang, 15)

# --- 8. For each top word, get count in full text (not just love sentences) ---
def get_fulltext_counts(lang, words):
    freq_all = corpora[lang]['freq_all']
    return {w: freq_all[w] for w in words}

fulltext_counts = {lang: get_fulltext_counts(lang, top_words[lang]) for lang in top_words}
fulltext_counts

# --- 9. PMI calculation ---
def calc_pmi(lang, word, smoothing=1):
    data = corpora[lang]
    total_sents = data['total_sents'] + smoothing
    total_love_sents = data['total_sents_love'] + smoothing
    # Count of sentences containing word (in any context)
    sents_with_word = sum(1 for s in data['sentences'] if word in s) + smoothing
    # Count of sentences in love_sentences containing the word
    sents_love_with_word = sum(1 for s in data['love_sentences'] if word in s) + smoothing
    p_love = total_love_sents / total_sents
    p_word = sents_with_word / total_sents
    p_both = sents_love_with_word / total_sents
    return math.log2(p_both / (p_love * p_word))

# --- 10. Show table: word, count in love sents, count in full text, PMI ---
def show_word_stats_table(lang, words):
    print(f"\n{'Word':<20}{'Love Cnt':>10}{'Full Cnt':>10}{'PMI':>12}")
    print('-'*44)
    for w in words:
        love_cnt = corpora[lang]['freq_love'][w]
        full_cnt = corpora[lang]['freq_all'][w]
        pmi = calc_pmi(lang, w)
        print(f"{w:<20}{love_cnt:>10}{full_cnt:>10}{pmi:>12.3f}")

for lang in ['korean', 'english', 'french']:
    print(f"\n{lang.capitalize()} top word stats:")
    show_word_stats_table(lang, top_words[lang])

# --- 11. Visualization (Bar chart for PMI) ---
def plot_pmi(lang, words):
    pmis = [calc_pmi(lang, w) for w in words]
    plt.figure(figsize=(9,4))
    plt.bar(range(len(words)), pmis)
    plt.xticks(range(len(words)), words, rotation=45, ha='right')
    plt.ylabel('PMI')
    plt.title(f"{lang.capitalize()} Top PMI Words (Love Sentences)")
    plt.tight_layout()
    plt.show()

for lang in ['korean', 'english', 'french']:
    plot_pmi(lang, top_words[lang])


def show_love_sentences_with_word(word, lang):
    """
    Print all love sentences in the specified language that contain the given word.
    """
    love_sentences = corpora[lang]['love_sentences']
    found = [s for s in love_sentences if word in s]
    print(f"\nTotal {len(found)} love sentences containing '{word}' in {lang.capitalize()}:\n")
    for i, sent in enumerate(found, 1):
        print(f"{i:>2}. {sent}")
    if not found:
        print("No sentence found.")

# Example usage:
show_love_sentences_with_word('꽃', 'korean')
# show_love_sentences_with_word('prince', 'english')
# show_love_sentences_with_word('prince', 'french')
